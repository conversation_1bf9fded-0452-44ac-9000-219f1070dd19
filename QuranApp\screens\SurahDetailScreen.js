import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Alert
} from 'react-native';
import { Audio } from 'expo-av';
import AyahCard from '../components/AyahCard';
import AudioPlayer from '../components/AudioPlayer';

const SurahDetailScreen = ({ route, navigation }) => {
  const { surah } = route.params;
  const [sound, setSound] = useState();
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    return sound
      ? () => {
          sound.unloadAsync();
        }
      : undefined;
  }, [sound]);

  const playAudio = async () => {
    try {
      setIsLoading(true);
      
      if (sound) {
        await sound.unloadAsync();
      }

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: surah.audioUrl },
        { shouldPlay: true }
      );
      
      setSound(newSound);
      setIsPlaying(true);
      setIsLoading(false);

      newSound.setOnPlaybackStatusUpdate((status) => {
        if (status.didJustFinish) {
          setIsPlaying(false);
        }
      });

    } catch (error) {
      setIsLoading(false);
      Alert.alert('خطأ', 'لا يمكن تشغيل التلاوة. تأكد من اتصالك بالإنترنت.');
      console.error('Error playing audio:', error);
    }
  };

  const pauseAudio = async () => {
    if (sound) {
      await sound.pauseAsync();
      setIsPlaying(false);
    }
  };

  const stopAudio = async () => {
    if (sound) {
      await sound.stopAsync();
      setIsPlaying(false);
    }
  };

  const renderAyah = (ayah, index) => (
    <AyahCard
      key={ayah.id}
      ayah={ayah}
      showTranslation={true}
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2E7D32" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>{surah.name}</Text>
          <Text style={styles.headerSubtitle}>
            {surah.nameEnglish} • {surah.verses} آية • {surah.type}
          </Text>
        </View>
      </View>

      {/* Audio Player */}
      <AudioPlayer
        audioUrl={surah.audioUrl}
        surahName={surah.name}
        onPlaybackStatusUpdate={(status) => {
          // يمكن إضافة منطق إضافي هنا
        }}
      />

      {/* Ayahs */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.bismillah}>
          <Text style={styles.bismillahText}>
            بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
          </Text>
        </View>
        
        {surah.ayahs.map((ayah, index) => renderAyah(ayah, index))}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    backgroundColor: '#2E7D32',
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 15,
    padding: 5,
  },
  backButtonText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#E8F5E8',
  },
  audioControls: {
    backgroundColor: 'white',
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
  },
  audioButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#2E7D32',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  audioButtonDisabled: {
    backgroundColor: '#999',
  },
  audioButtonText: {
    color: 'white',
    fontSize: 20,
  },
  audioLabel: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  stopButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#D32F2F',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stopButtonText: {
    color: 'white',
    fontSize: 16,
  },
  content: {
    flex: 1,
    padding: 15,
  },
  bismillah: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 10,
    elevation: 1,
  },
  bismillahText: {
    fontSize: 18,
    color: '#2E7D32',
    fontWeight: 'bold',
  },
  ayahContainer: {
    backgroundColor: 'white',
    marginBottom: 15,
    padding: 15,
    borderRadius: 10,
    elevation: 1,
  },
  ayahNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#2E7D32',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  ayahNumberText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  ayahText: {
    fontSize: 20,
    lineHeight: 35,
    textAlign: 'right',
    color: '#333',
    marginBottom: 10,
  },
  ayahTranslation: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
    fontStyle: 'italic',
  },
});

export default SurahDetailScreen;
