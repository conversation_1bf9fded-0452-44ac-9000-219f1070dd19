# تطبيق القرآن الكريم - Holy Quran App

تطبيق React Native بسيط وجميل للقرآن الكريم مع تلاوة الشيخ أحمد العجمي.

## المميزات

- 📖 عرض سور القرآن الكريم
- 🎵 تلاوة صوتية بصوت الشيخ أحمد العجمي
- 🎨 تصميم إسلامي جميل ومريح للعين
- 📱 متوافق مع Android و iOS
- 🌐 ترجمة إنجليزية للآيات
- ⚡ أداء سريع وسلس

## التقنيات المستخدمة

- **React Native** - إطار العمل الأساسي
- **Expo** - منصة التطوير والنشر
- **React Navigation** - للتنقل بين الشاشات
- **Expo AV** - لتشغيل الملفات الصوتية
- **JavaScript** - لغة البرمجة

## بنية المشروع

```
QuranApp/
├── components/          # المكونات القابلة لإعادة الاستخدام
│   ├── AudioPlayer.js   # مشغل الصوت
│   └── AyahCard.js      # بطاقة عرض الآية
├── screens/             # شاشات التطبيق
│   ├── WelcomeScreen.js # شاشة الترحيب
│   ├── SurahListScreen.js # شاشة قائمة السور
│   └── SurahDetailScreen.js # شاشة عرض السورة
├── data/                # بيانات التطبيق
│   └── quranData.js     # بيانات القرآن الكريم
└── assets/              # الملفات الثابتة
    └── audio/           # ملفات الصوت
```

## كيفية التشغيل

### المتطلبات
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn
- تطبيق Expo Go على الهاتف

### خطوات التشغيل

1. **تثبيت المتطلبات:**
   ```bash
   npm install
   ```

2. **تشغيل التطبيق:**
   ```bash
   npx expo start
   ```

3. **فتح التطبيق:**
   - امسح رمز QR بتطبيق Expo Go (Android)
   - امسح رمز QR بتطبيق الكاميرا (iOS)
   - أو اضغط 'w' لفتح التطبيق في المتصفح

## الشاشات

### 1. شاشة الترحيب
- تعرض شعار التطبيق
- معلومات عن القارئ
- انتقال تلقائي لشاشة السور

### 2. شاشة قائمة السور
- قائمة بجميع سور القرآن
- معلومات كل سورة (عدد الآيات، مكية/مدنية)
- إمكانية البحث والتنقل

### 3. شاشة عرض السورة
- عرض آيات السورة
- مشغل صوتي للتلاوة
- ترجمة إنجليزية
- تصميم مريح للقراءة

## المميزات الصوتية

- **القارئ:** الشيخ أحمد العجمي
- **جودة الصوت:** عالية
- **التحكم:** تشغيل، إيقاف، إيقاف مؤقت
- **شريط التقدم:** يعرض موضع التلاوة الحالي

## التخصيص

يمكنك تخصيص التطبيق من خلال:

1. **إضافة سور جديدة:** تحديث ملف `data/quranData.js`
2. **تغيير القارئ:** تحديث روابط الصوت في البيانات
3. **تعديل التصميم:** تحديث ملفات الأنماط في كل مكون
4. **إضافة لغات:** إضافة ترجمات جديدة في البيانات

## الإصدارات المستقبلية

- [ ] إضافة المزيد من السور
- [ ] إضافة قراء متعددين
- [ ] وضع القراءة الليلية
- [ ] حفظ الإعدادات المفضلة
- [ ] إضافة التفسير
- [ ] البحث في النص

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والشخصي.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح Issue في المستودع.

---

**بارك الله فيكم وجعل هذا العمل في ميزان حسناتنا** 🤲
