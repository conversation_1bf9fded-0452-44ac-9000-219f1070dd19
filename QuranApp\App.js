import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import WelcomeScreen from './screens/WelcomeScreen';
import SurahListScreen from './screens/SurahListScreen';
import SurahDetailScreen from './screens/SurahDetailScreen';

const Stack = createStackNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <Stack.Navigator
        initialRouteName="Welcome"
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen
          name="Welcome"
          component={WelcomeScreen}
          options={{ title: 'مرحباً' }}
        />
        <Stack.Screen
          name="SurahList"
          component={SurahListScreen}
          options={{ title: 'القرآن الكريم' }}
        />
        <Stack.Screen
          name="SurahDetail"
          component={SurahDetailScreen}
          options={{ title: 'السورة' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
