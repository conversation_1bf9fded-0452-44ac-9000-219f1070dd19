import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';

const AyahCard = ({ ayah, onPress, showTranslation = true }) => {
  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={() => onPress && onPress(ayah)}
      activeOpacity={0.7}
    >
      <View style={styles.header}>
        <View style={styles.ayahNumber}>
          <Text style={styles.ayahNumberText}>{ayah.id}</Text>
        </View>
        <View style={styles.decorativeLine} />
      </View>
      
      <Text style={styles.ayahText}>{ayah.text}</Text>
      
      {showTranslation && ayah.translation && (
        <View style={styles.translationContainer}>
          <Text style={styles.translationText}>{ayah.translation}</Text>
        </View>
      )}
      
      <View style={styles.footer}>
        <View style={styles.decorativeDots}>
          <Text style={styles.dots}>• • •</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    marginVertical: 8,
    marginHorizontal: 15,
    borderRadius: 15,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    borderLeftWidth: 4,
    borderLeftColor: '#2E7D32',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  ayahNumber: {
    width: 35,
    height: 35,
    borderRadius: 17.5,
    backgroundColor: '#2E7D32',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  ayahNumberText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  decorativeLine: {
    flex: 1,
    height: 2,
    backgroundColor: '#E8F5E8',
    borderRadius: 1,
  },
  ayahText: {
    fontSize: 22,
    lineHeight: 40,
    textAlign: 'right',
    color: '#1B5E20',
    marginBottom: 15,
    fontWeight: '500',
  },
  translationContainer: {
    backgroundColor: '#F1F8E9',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
  },
  translationText: {
    fontSize: 15,
    lineHeight: 22,
    color: '#2E7D32',
    fontStyle: 'italic',
  },
  footer: {
    alignItems: 'center',
    marginTop: 5,
  },
  decorativeDots: {
    paddingVertical: 5,
  },
  dots: {
    color: '#81C784',
    fontSize: 16,
    letterSpacing: 3,
  },
});

export default AyahCard;
