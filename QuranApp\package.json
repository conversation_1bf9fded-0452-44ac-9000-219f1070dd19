{"name": "qura<PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "expo": "~54.0.12", "expo-av": "^16.0.7", "expo-status-bar": "~3.0.8", "react": "19.1.0", "react-native": "0.81.4", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0"}, "private": true}